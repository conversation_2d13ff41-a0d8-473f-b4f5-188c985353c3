PODS:
  - app_device_integrity (0.0.1):
    - Flutter
  - AppAuth (1.7.6):
    - AppAuth/Core (= 1.7.6)
    - AppAuth/ExternalUserAgent (= 1.7.6)
  - AppAuth/Core (1.7.6)
  - AppAuth/ExternalUserAgent (1.7.6):
    - AppAuth/Core
  - "appcheck (1.5.4+1)":
    - Flutter
  - AppCheckCore (11.2.0):
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - PromisesObjC (~> 2.4)
  - coinbase_wallet_sdk (0.0.1):
    - CoinbaseWalletSDK/CrossPlatform (= 1.0.4)
    - Flutter
  - CoinbaseWalletSDK/Client (1.0.4)
  - CoinbaseWalletSDK/CrossPlatform (1.0.4):
    - CoinbaseWalletSDK/Client
  - connectivity_plus (0.0.1):
    - Flutter
  - device_check (0.0.1):
    - Flutter
  - device_info_plus (0.0.1):
    - Flutter
  - DKImagePickerController/Core (4.3.9):
    - DKImagePickerController/ImageDataManager
    - DKImagePickerController/Resource
  - DKImagePickerController/ImageDataManager (4.3.9)
  - DKImagePickerController/PhotoGallery (4.3.9):
    - DKImagePickerController/Core
    - DKPhotoGallery
  - DKImagePickerController/Resource (4.3.9)
  - DKPhotoGallery (0.0.19):
    - DKPhotoGallery/Core (= 0.0.19)
    - DKPhotoGallery/Model (= 0.0.19)
    - DKPhotoGallery/Preview (= 0.0.19)
    - DKPhotoGallery/Resource (= 0.0.19)
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Core (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Preview
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Model (0.0.19):
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Preview (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Resource
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Resource (0.0.19):
    - SDWebImage
    - SwiftyGif
  - file_picker (0.0.1):
    - DKImagePickerController/PhotoGallery
    - Flutter
  - Firebase/CoreOnly (12.0.0):
    - FirebaseCore (~> 12.0.0)
  - firebase_core (4.0.0):
    - Firebase/CoreOnly (= 12.0.0)
    - Flutter
  - FirebaseCore (12.0.0):
    - FirebaseCoreInternal (~> 12.0.0)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/Logger (~> 8.1)
  - FirebaseCoreInternal (12.0.0):
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
  - Flutter (1.0.0)
  - flutter_inappwebview_ios (0.0.1):
    - Flutter
    - flutter_inappwebview_ios/Core (= 0.0.1)
    - OrderedSet (~> 6.0.3)
  - flutter_inappwebview_ios/Core (0.0.1):
    - Flutter
    - OrderedSet (~> 6.0.3)
  - flutter_secure_storage (6.0.0):
    - Flutter
  - fluttertoast (0.0.2):
    - Flutter
  - google_sign_in_ios (0.0.1):
    - AppAuth (>= 1.7.4)
    - Flutter
    - FlutterMacOS
    - GoogleSignIn (~> 8.0)
    - GTMSessionFetcher (>= 3.4.0)
  - GoogleSignIn (8.0.0):
    - AppAuth (< 2.0, >= 1.7.3)
    - AppCheckCore (~> 11.0)
    - GTMAppAuth (< 5.0, >= 4.1.1)
    - GTMSessionFetcher/Core (~> 3.3)
  - GoogleUtilities/Environment (8.1.0):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - "GoogleUtilities/NSData+zlib (8.1.0)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.1.0)
  - GoogleUtilities/UserDefaults (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GTMAppAuth (4.1.1):
    - AppAuth/Core (~> 1.7)
    - GTMSessionFetcher/Core (< 4.0, >= 3.3)
  - GTMSessionFetcher (3.5.0):
    - GTMSessionFetcher/Full (= 3.5.0)
  - GTMSessionFetcher/Core (3.5.0)
  - GTMSessionFetcher/Full (3.5.0):
    - GTMSessionFetcher/Core
  - image_cropper (0.0.4):
    - Flutter
    - TOCropViewController (~> 2.7.4)
  - image_picker_ios (0.0.1):
    - Flutter
  - local_auth_darwin (0.0.1):
    - Flutter
    - FlutterMacOS
  - mobile_scanner (7.0.0):
    - Flutter
    - FlutterMacOS
  - nearby_service (0.0.1):
    - Flutter
    - FlutterMacOS
  - OrderedSet (6.0.3)
  - package_info_plus (0.4.5):
    - Flutter
  - passkeys_ios (0.0.1):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - PromisesObjC (2.4.0)
  - SDWebImage (5.21.1):
    - SDWebImage/Core (= 5.21.1)
  - SDWebImage/Core (5.21.1)
  - sensors_plus (0.0.1):
    - Flutter
  - share_plus (0.0.1):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sign_in_with_apple (0.0.1):
    - Flutter
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - SwiftyGif (5.4.5)
  - TOCropViewController (2.7.4)
  - ua_client_hints (1.4.1):
    - Flutter
  - url_launcher_ios (0.0.1):
    - Flutter
  - webview_flutter_wkwebview (0.0.1):
    - Flutter
    - FlutterMacOS

DEPENDENCIES:
  - app_device_integrity (from `.symlinks/plugins/app_device_integrity/ios`)
  - appcheck (from `.symlinks/plugins/appcheck/ios`)
  - coinbase_wallet_sdk (from `.symlinks/plugins/coinbase_wallet_sdk/ios`)
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - device_check (from `.symlinks/plugins/device_check/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - file_picker (from `.symlinks/plugins/file_picker/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - Flutter (from `Flutter`)
  - flutter_inappwebview_ios (from `.symlinks/plugins/flutter_inappwebview_ios/ios`)
  - flutter_secure_storage (from `.symlinks/plugins/flutter_secure_storage/ios`)
  - fluttertoast (from `.symlinks/plugins/fluttertoast/ios`)
  - google_sign_in_ios (from `.symlinks/plugins/google_sign_in_ios/darwin`)
  - image_cropper (from `.symlinks/plugins/image_cropper/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - local_auth_darwin (from `.symlinks/plugins/local_auth_darwin/darwin`)
  - mobile_scanner (from `.symlinks/plugins/mobile_scanner/darwin`)
  - nearby_service (from `.symlinks/plugins/nearby_service/darwin`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - passkeys_ios (from `.symlinks/plugins/passkeys_ios/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - sensors_plus (from `.symlinks/plugins/sensors_plus/ios`)
  - share_plus (from `.symlinks/plugins/share_plus/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sign_in_with_apple (from `.symlinks/plugins/sign_in_with_apple/ios`)
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)
  - ua_client_hints (from `.symlinks/plugins/ua_client_hints/ios`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/darwin`)

SPEC REPOS:
  trunk:
    - AppAuth
    - AppCheckCore
    - CoinbaseWalletSDK
    - DKImagePickerController
    - DKPhotoGallery
    - Firebase
    - FirebaseCore
    - FirebaseCoreInternal
    - GoogleSignIn
    - GoogleUtilities
    - GTMAppAuth
    - GTMSessionFetcher
    - OrderedSet
    - PromisesObjC
    - SDWebImage
    - SwiftyGif
    - TOCropViewController

EXTERNAL SOURCES:
  app_device_integrity:
    :path: ".symlinks/plugins/app_device_integrity/ios"
  appcheck:
    :path: ".symlinks/plugins/appcheck/ios"
  coinbase_wallet_sdk:
    :path: ".symlinks/plugins/coinbase_wallet_sdk/ios"
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  device_check:
    :path: ".symlinks/plugins/device_check/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  file_picker:
    :path: ".symlinks/plugins/file_picker/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  Flutter:
    :path: Flutter
  flutter_inappwebview_ios:
    :path: ".symlinks/plugins/flutter_inappwebview_ios/ios"
  flutter_secure_storage:
    :path: ".symlinks/plugins/flutter_secure_storage/ios"
  fluttertoast:
    :path: ".symlinks/plugins/fluttertoast/ios"
  google_sign_in_ios:
    :path: ".symlinks/plugins/google_sign_in_ios/darwin"
  image_cropper:
    :path: ".symlinks/plugins/image_cropper/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  local_auth_darwin:
    :path: ".symlinks/plugins/local_auth_darwin/darwin"
  mobile_scanner:
    :path: ".symlinks/plugins/mobile_scanner/darwin"
  nearby_service:
    :path: ".symlinks/plugins/nearby_service/darwin"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  passkeys_ios:
    :path: ".symlinks/plugins/passkeys_ios/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  sensors_plus:
    :path: ".symlinks/plugins/sensors_plus/ios"
  share_plus:
    :path: ".symlinks/plugins/share_plus/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sign_in_with_apple:
    :path: ".symlinks/plugins/sign_in_with_apple/ios"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"
  ua_client_hints:
    :path: ".symlinks/plugins/ua_client_hints/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/darwin"

SPEC CHECKSUMS:
  app_device_integrity: 9be0287fa5c96c408b25a6163c7a5138777a9cf6
  AppAuth: d4f13a8fe0baf391b2108511793e4b479691fb73
  appcheck: ****************************************
  AppCheckCore: cc8fd0a3a230ddd401f326489c99990b013f0c4f
  coinbase_wallet_sdk: 7ccd4e1a7940deba6ba9bd81beece999a2268c15
  CoinbaseWalletSDK: ea1f37512bbc69ebe07416e3b29bf840f5cc3152
  connectivity_plus: 2a701ffec2c0ae28a48cf7540e279787e77c447d
  device_check: adcb18c7fe1f400e313747a97e26c568d3bbb0b8
  device_info_plus: bf2e3232933866d73fe290f2942f2156cdd10342
  DKImagePickerController: 946cec48c7873164274ecc4624d19e3da4c1ef3c
  DKPhotoGallery: b3834fecb755ee09a593d7c9e389d8b5d6deed60
  file_picker: b159e0c068aef54932bb15dc9fd1571818edaf49
  Firebase: 800d487043c0557d9faed71477a38d9aafb08a41
  firebase_core: c70f903d70ec229c08c2f49aba5a8c1338546d40
  FirebaseCore: 055f4ab117d5964158c833f3d5e7ec6d91648d4a
  FirebaseCoreInternal: dedc28e569a4be85f38f3d6af1070a2e12018d55
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_inappwebview_ios: 6f63631e2c62a7c350263b13fa5427aedefe81d4
  flutter_secure_storage: d33dac7ae2ea08509be337e775f6b59f1ff45f12
  fluttertoast: 21eecd6935e7064cc1fcb733a4c5a428f3f24f0f
  google_sign_in_ios: 7411fab6948df90490dc4620ecbcabdc3ca04017
  GoogleSignIn: ce8c89bb9b37fb624b92e7514cc67335d1e277e4
  GoogleUtilities: 00c88b9a86066ef77f0da2fab05f65d7768ed8e1
  GTMAppAuth: f69bd07d68cd3b766125f7e072c45d7340dea0de
  GTMSessionFetcher: 5aea5ba6bd522a239e236100971f10cb71b96ab6
  image_cropper: e0bb0042e4404ff2ef134e5cf0492cbd892156cd
  image_picker_ios: c560581cceedb403a6ff17f2f816d7fea1421fc1
  local_auth_darwin: fa4b06454df7df8e97c18d7ee55151c57e7af0de
  mobile_scanner: 77265f3dc8d580810e91849d4a0811a90467ed5e
  nearby_service: 89cfcb10c1b5a1a7f3807449b07ca1399134b66e
  OrderedSet: e539b66b644ff081c73a262d24ad552a69be3a94
  package_info_plus: c0502532a26c7662a62a356cebe2692ec5fe4ec4
  passkeys_ios: fdae8c06e2178a9fcb9261a6cb21fb9a06a81d53
  path_provider_foundation: 2b6b4c569c0fb62ec74538f866245ac84301af46
  permission_handler_apple: 9878588469a2b0d0fc1e048d9f43605f92e6cec2
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  SDWebImage: f29024626962457f3470184232766516dee8dfea
  sensors_plus: 7229095999f30740798f0eeef5cd120357a8f4f2
  share_plus: 8b6f8b3447e494cca5317c8c3073de39b3600d1f
  shared_preferences_foundation: fcdcbc04712aee1108ac7fda236f363274528f78
  sign_in_with_apple: f3bf75217ea4c2c8b91823f225d70230119b8440
  sqflite_darwin: 5a7236e3b501866c1c9befc6771dfd73ffb8702d
  SwiftyGif: 706c60cf65fa2bc5ee0313beece843c8eb8194d4
  TOCropViewController: 80b8985ad794298fb69d3341de183f33d1853654
  ua_client_hints: aeabd123262c087f0ce151ef96fa3ab77bfc8b38
  url_launcher_ios: 5334b05cef931de560670eeae103fd3e431ac3fe
  webview_flutter_wkwebview: a4af96a051138e28e29f60101d094683b9f82188

PODFILE CHECKSUM: 60d7867f0061b077d517737900ac115bff769a00

COCOAPODS: 1.16.2
