import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import 'package:toii_social/model/auth/attestation_nonce/attestation_nonce_model.dart';
import 'package:toii_social/model/auth/change_password/change_password_request_model.dart';
import 'package:toii_social/model/auth/change_password/change_password_response_model.dart';
import 'package:toii_social/model/auth/login/login_model.dart';
import 'package:toii_social/model/auth/login_gmail/login_gmail_request_model.dart';
import 'package:toii_social/model/auth/login_wallet/login_wallet.dart';
import 'package:toii_social/model/auth/otp/request_otp.dart';
import 'package:toii_social/model/auth/register/verify_otp_model.dart';
import 'package:toii_social/model/auth/register_update_info/register_update_info_model.dart';
import 'package:toii_social/model/auth/username_availability/username_availability_response_model.dart';
import 'package:toii_social/model/auth/wallet/wallet_message_auth_model.dart';
import 'package:toii_social/model/auth/wallet_nonce/wallet_nonce_model.dart';
import 'package:toii_social/model/base/base_response.dart';
import 'package:toii_social/model/user/user_model.dart';

part 'auth_service.g.dart';

@RestApi()
abstract class AuthService {
  factory AuthService(Dio dio, {String baseUrl}) = _AuthService;

  @POST('/auth/api/v1/auth/login')
  Future<BaseResponse<LoginModel>> login(@Body() LoginRequestModel request);

  @GET('/auth/api/v1/auth/wallet-nonce')
  Future<BaseResponse<WalletNonceModel>> getWalletNonce(
    @Query('address') String address,
  );


  @POST('/auth/api/v1/auth/wallet')
  Future<BaseResponse<LoginModel>> loginOrRegisterWallet(
    @Body() WalletRegisterNonceModel request,
  );

  @GET('/auth/api/v1/auth/attestation/nonce')
  Future<BaseResponse<AttestationNonceModel>> getAttestationNonce();

  @POST('/auth/api/v1/auth/attestation/register')
  Future<BaseResponse<LoginModel>> registerAttestation(
    @Body() AttestationRegisterRequestModel request,
  );

  @GET('/user/api/v1/users/me')
  Future<BaseResponse<UserModel>> getProfile();
  @POST('/auth/api/v1/auth/login-wallet')
  Future<BaseResponse<LoginModel>> loginWallet(
    @Body() WalletLoginRequestModel request,
  );

  @POST('/auth/api/v1/auth/request-otp')
  Future<BaseResponse<void>> requestOtp(@Body() RequestOtpModel request);

  @POST('/auth/api/v1/auth/verify-otp')
  Future<BaseResponse<VerifyOtpResponseModel>> verifyOtp(
    @Body() RequestVerifyOtpModel request,
  );

  @PUT('/auth/api/v1/auth/complete-registration')
  Future<BaseResponse<LoginModel>> completeRegistration(
    @Body() RegisterUpdateInfoRequestModel request,
  );

  @POST('/auth/api/v1/auth/social/google/tokens')
  Future<BaseResponse<LoginModel>> tokenWithGmail(
    @Body() LoginGmailRequestModel request,
  );

  @POST('/auth/api/v1/auth/social/apple/tokens')
  Future<BaseResponse<LoginModel>> tokenWithApple(
    @Body() LoginAppleRequestModel request,
  );

  @GET('/auth/api/v1/auth/wallet-nonce')
  Future<BaseResponse<WalletMessageAuthModel>>
  getNonceMessageForWalletSignature(@Query('address') String address);

  @PUT('/auth/api/v1/auth/users/change-password')
  Future<BaseResponse<ChangePasswordResponseModel>> changePassword(
    @Body() ChangePasswordRequestModel request,
  );

  @DELETE('/user/api/v1/users/{userId}')
  Future<BaseResponse<void>> deleteAccount(@Path('userId') String userId);

  @GET('/user/api/v1/public/username/{username}/availability')
  Future<BaseResponse<UsernameAvailabilityResponseModel>>
  checkUsernameAvailability(@Path('username') String username);
}
