import 'dart:convert';
import 'dart:typed_data';

import 'package:dio/dio.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:toii_social/core/constant/key_shared.dart';
import 'package:toii_social/core/repository/auth_repository.dart';
import 'package:toii_social/core/service/wallet_service.dart';
import 'package:toii_social/model/auth/wallet_nonce/wallet_nonce_model.dart';
import 'package:toii_social/model/credentials/credentials.dart';
import 'package:toii_social/utils/keychain/keychain_service.dart';
import 'package:toii_social/utils/shared_prefs/shared_prefs.dart';
import 'package:web3dart/crypto.dart';
import 'package:web3dart/web3dart.dart' hide Credentials;

part 'create_account_state.dart';

class CreateAccountCubit extends Cubit<CreateAccountState> {
  CreateAccountCubit() : super(const CreateAccountState());

  final AuthRepository _authRepository = GetIt.instance<AuthRepository>();

  void createAccount(String? address) async {
    emit(state.copyWith(status: CreateAccountStatus.loading));
    Credentials wallet;
    if (address == null) {
      wallet = await WalletService.createWallet();
    } else {
      final privateKey = await KeychainService.instance
          .readPrivateKeyFromiCloud(address);
      wallet = Credentials(address: address, privateKeyHex: privateKey ?? "");
    }
    final result = await _authRepository.getWalletNonce(wallet.address);
    final sign = await personalSignByPrivateKey(
      privateKeyHex: wallet.privateKeyHex,
      message: result.data.message,
    );

    final request = WalletRegisterNonceModel(
      address: wallet.address,
      signature: sign,
      networkBase: "ethereum",
      walletProvider: "metamask",
    );
    final loginResponse = await _authRepository.loginOrRegisterWallet(request);
    SharedPref.setBool(KeyShared.isLogin, true);
    SharedPref.setString(KeyShared.tokenKey, loginResponse.data.accessToken);
    KeychainService.instance.savePrivateKeyToiCloud(
      credentials: Credentials(
        address: wallet.address,
        privateKeyHex: wallet.privateKeyHex,
      ),
    );
    emit(state.copyWith(status: CreateAccountStatus.success));
  }

  Future<void> getAttestationNonce() async {
    try {
      emit(state.copyWith(status: CreateAccountStatus.loading));

      // Call the delete account API
      final result = await _authRepository.getAttestationNonce();
      registerAttestation(result.data.nonce);
      // emit(
      //   state.copyWith(
      //     status: CreateAccountStatus.success,
      //     message: 'Account deleted successfully',
      //     errorMessage: null,
      //   ),
      // );
    } on DioException catch (e) {
      emit(
        state.copyWith(
          status: CreateAccountStatus.failure,
          errorMessage: e.response?.data['message'] ?? e.toString(),
        ),
      );
    } on Exception catch (e) {
      emit(
        state.copyWith(
          status: CreateAccountStatus.failure,
          errorMessage: e.toString(),
        ),
      );
    }
  }

  void registerAttestation(String nonceKey) async {
    try {
      // final keyId = await AppAttestService.instance.generateKey();
      // var outputAsUint8List = Uint8List.fromList(nonceKey.codeUnits);

      // Uint8List attestation = await AppAttestService.instance.attestKey(
      //   keyId: keyId,
      //   clientDataHash: outputAsUint8List,
      // );
      // final attestationStr = base64Encode(attestation);
      // final abc = utf8.decode(attestation);
      // final request = AttestationRegisterRequestModel(
      //   keyId: nonceKey,
      //   attestationData: attestationStr,
      //   networkBase: "ethereum",
      //   walletAddress: "******************************************",
      //   walletProvider: "metamask",
      //   // required this.walletProvider,
      // );

      // final response = await _authRepository.registerAttestation(request);
      //  emit(state.copyWith(status: CreateAccountStatus.loading));

      // emit(
      //   state.copyWith(
      //     status: CreateAccountStatus.success,
      //     message: 'Account created successfully',
      //     errorMessage: null,
      //   ),
      // );
    } on DioException catch (e) {
      emit(
        state.copyWith(
          status: CreateAccountStatus.failure,
          errorMessage: e.response?.data['message'] ?? e.toString(),
        ),
      );
    } on Exception catch (e) {
      emit(
        state.copyWith(
          status: CreateAccountStatus.failure,
          errorMessage: e.toString(),
        ),
      );
    }
  }
}

Future<String> personalSignByPrivateKey({
  required String privateKeyHex, // e.g. '0xabc...'
  required String message,
}) async {
  final key = EthPrivateKey.fromHex(privateKeyHex);
  final msgBytes = Uint8List.fromList(utf8.encode(message));

  // web3dart applies the EIP-191 prefix internally for "personal" signing
  final sigBytes = key.signPersonalMessageToUint8List(msgBytes);

  return bytesToHex(sigBytes, include0x: true);
}
