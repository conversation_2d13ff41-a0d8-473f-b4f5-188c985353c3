import 'package:dio/dio.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:toii_social/core/repository/user_repository.dart';
import 'package:toii_social/cubit/auth/profile/profile_cubit.dart';
import 'package:toii_social/model/user/update_user_request_model.dart';
import 'package:toii_social/model/user/user_model.dart';

part 'edit_profile_state.dart';

class EditProfileCubit extends Cubit<EditProfileState> {
  EditProfileCubit() : super(const EditProfileState());

  final UserRepository _userRepository = GetIt.instance<UserRepository>();

  String? _originalUsername;
  String? _originalDisplayName;
  String? _originalBio;
  String? _originalAvatar;
  String? _originalBackgroundImage;

  void initializeProfile(UserModel user) {
    _originalUsername = user.username;
    _originalDisplayName = user.fullName ?? user.firstName;
    _originalBio = user.bio;
    _originalAvatar = user.avatar;
    _originalBackgroundImage = user.backgroundUrl;

    emit(
      state.copyWith(
        username: _originalUsername,
        displayName: _originalDisplayName,
        bio: _originalBio,
        avatar: _originalAvatar,
        backgroundImage: _originalBackgroundImage,
        hasChanges: false,
      ),
    );
  }

  void updateUsername(String username) {
    final hasChanges = _hasChanges(
      username: username,
      displayName: state.displayName,
      bio: state.bio,
      avatar: state.avatar,
      backgroundImage: state.backgroundImage,
    );

    emit(state.copyWith(username: username, hasChanges: hasChanges));
  }

  void updateDisplayName(String displayName) {
    final hasChanges = _hasChanges(
      username: state.username,
      displayName: displayName,
      bio: state.bio,
      avatar: state.avatar,
      backgroundImage: state.backgroundImage,
    );

    emit(state.copyWith(displayName: displayName, hasChanges: hasChanges));
  }

  void updateBio(String bio) {
    final hasChanges = _hasChanges(
      username: state.username,
      displayName: state.displayName,
      bio: bio,
      avatar: state.avatar,
      backgroundImage: state.backgroundImage,
    );

    emit(state.copyWith(bio: bio, hasChanges: hasChanges));
  }

  void updateAvatar(String avatar) {
    final hasChanges = _hasChanges(
      username: state.username,
      displayName: state.displayName,
      bio: state.bio,
      avatar: avatar,
      backgroundImage: state.backgroundImage,
    );

    emit(state.copyWith(avatar: avatar, hasChanges: hasChanges));
  }

  void updateBackgroundImage(String backgroundImage) {
    final hasChanges = _hasChanges(
      username: state.username,
      displayName: state.displayName,
      bio: state.bio,
      avatar: state.avatar,
      backgroundImage: backgroundImage,
    );

    emit(
      state.copyWith(backgroundImage: backgroundImage, hasChanges: hasChanges),
    );
  }

  bool _hasChanges({
    String? username,
    String? displayName,
    String? bio,
    String? avatar,
    String? backgroundImage,
  }) {
    return username != _originalUsername ||
        displayName != _originalDisplayName ||
        bio != _originalBio ||
        avatar != _originalAvatar ||
        backgroundImage != _originalBackgroundImage;
  }

  Future<void> saveProfile(String userId) async {
    if (!state.hasChanges) return;

    emit(state.copyWith(status: EditProfileStatus.loading));

    try {
      final request = UpdateUserRequestModel(
        username: state.username,
        fullName: state.displayName,
        bio: state.bio,
        avatar: state.avatar,
        backgroundImage: state.backgroundImage,
      );

      await _userRepository.updateUser(userId, request);

      // Update original values after successful save
      _originalUsername = state.username;
      _originalDisplayName = state.displayName;
      _originalBio = state.bio;
      _originalAvatar = state.avatar;
      _originalBackgroundImage = state.backgroundImage;

      emit(
        state.copyWith(status: EditProfileStatus.success, hasChanges: false),
      );
      GetIt.instance<ProfileCubit>().getProfile();
    } on DioException catch (e) {
      emit(
        state.copyWith(
          status: EditProfileStatus.failure,
          errorMessage: e.message ?? 'Network error occurred',
        ),
      );
    } catch (e) {
      emit(
        state.copyWith(
          status: EditProfileStatus.failure,
          errorMessage: 'An unexpected error occurred',
        ),
      );
    }
  }

  Future<void> updateAvatarWithKey(String userId, String keyImage) async {
    emit(state.copyWith(status: EditProfileStatus.loading));

    try {
      final request = UpdateUserRequestModel(
        username: state.username,
        fullName: state.displayName,
        bio: state.bio,
        avatar: keyImage,
      );

      await _userRepository.updateUser(userId, request);

      // Update original and current avatar values after successful update
      _originalAvatar = keyImage;

      emit(
        state.copyWith(
          status: EditProfileStatus.success,
          avatar: keyImage,
          hasChanges: _hasChanges(
            username: state.username,
            displayName: state.displayName,
            bio: state.bio,
            avatar: keyImage,
          ),
        ),
      );
    } on DioException catch (e) {
      emit(
        state.copyWith(
          status: EditProfileStatus.failure,
          errorMessage: e.message ?? 'Network error occurred',
        ),
      );
    } catch (e) {
      emit(
        state.copyWith(
          status: EditProfileStatus.failure,
          errorMessage: 'An unexpected error occurred',
        ),
      );
    }
  }

  Future<void> updateBackgroundImageWithKey(
    String userId,
    String keyImage,
  ) async {
    print(
      'EditProfile: updateBackgroundImageWithKey called with userId=$userId, keyImage=$keyImage',
    );
    emit(state.copyWith(status: EditProfileStatus.loading));

    try {
      final request = UpdateUserRequestModel(
        username: state.username,
        fullName: state.displayName,
        bio: state.bio,
        avatar: state.avatar,
        backgroundImage: keyImage,
      );

      print(
        'EditProfile: Calling userRepository.updateUser with request: ${request.toJson()}',
      );
      await _userRepository.updateUser(userId, request);

      // Update original and current background image values after successful update
      _originalBackgroundImage = keyImage;

      print('EditProfile: Profile update successful, emitting success state');
      emit(
        state.copyWith(
          status: EditProfileStatus.success,
          backgroundImage: keyImage,
          hasChanges: _hasChanges(
            username: state.username,
            displayName: state.displayName,
            bio: state.bio,
            avatar: state.avatar,
            backgroundImage: keyImage,
          ),
        ),
      );
    } on DioException catch (e) {
      print('EditProfile: DioException occurred: ${e.message}');
      emit(
        state.copyWith(
          status: EditProfileStatus.failure,
          errorMessage: e.message ?? 'Network error occurred',
        ),
      );
    } catch (e) {
      print('EditProfile: Unexpected error occurred: ${e.toString()}');
      emit(
        state.copyWith(
          status: EditProfileStatus.failure,
          errorMessage: 'An unexpected error occurred: ${e.toString()}',
        ),
      );
    }
  }

  void resetState() {
    emit(const EditProfileState());
  }
}
