// GENERATED CODE - DO NOT MODIFY BY HAND
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'intl/messages_all.dart';

// **************************************************************************
// Generator: Flutter Intl IDE plugin
// Made by Localizely
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, lines_longer_than_80_chars
// ignore_for_file: join_return_with_assignment, prefer_final_in_for_each
// ignore_for_file: avoid_redundant_argument_values, avoid_escaping_inner_quotes

class S {
  S();

  static S? _current;

  static S get current {
    assert(
      _current != null,
      'No instance of S was loaded. Try to initialize the S delegate before accessing S.current.',
    );
    return _current!;
  }

  static const AppLocalizationDelegate delegate = AppLocalizationDelegate();

  static Future<S> load(Locale locale) {
    final name =
        (locale.countryCode?.isEmpty ?? false)
            ? locale.languageCode
            : locale.toString();
    final localeName = Intl.canonicalizedLocale(name);
    return initializeMessages(localeName).then((_) {
      Intl.defaultLocale = localeName;
      final instance = S();
      S._current = instance;

      return instance;
    });
  }

  static S of(BuildContext context) {
    final instance = S.maybeOf(context);
    assert(
      instance != null,
      'No instance of S present in the widget tree. Did you add S.delegate in localizationsDelegates?',
    );
    return instance!;
  }

  static S? maybeOf(BuildContext context) {
    return Localizations.of<S>(context, S);
  }

  /// `Sign in`
  String get start_login {
    return Intl.message('Sign in', name: 'start_login', desc: '', args: []);
  }

  /// `Register`
  String get start_register {
    return Intl.message('Register', name: 'start_register', desc: '', args: []);
  }

  /// `Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor `
  String get start_description {
    return Intl.message(
      'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor ',
      name: 'start_description',
      desc: '',
      args: [],
    );
  }

  /// `Welcome to GAO Social`
  String get start_welcome {
    return Intl.message(
      'Welcome to GAO Social',
      name: 'start_welcome',
      desc: '',
      args: [],
    );
  }

  /// `Welcome Back!`
  String get login_title {
    return Intl.message(
      'Welcome Back!',
      name: 'login_title',
      desc: '',
      args: [],
    );
  }

  /// `Welcome to `
  String get onboarding_welcome {
    return Intl.message(
      'Welcome to ',
      name: 'onboarding_welcome',
      desc: '',
      args: [],
    );
  }

  /// `EXPLORE THE FUTURE`
  String get onboarding_explore_the_future {
    return Intl.message(
      'EXPLORE THE FUTURE',
      name: 'onboarding_explore_the_future',
      desc: '',
      args: [],
    );
  }

  /// `GAO Social`
  String get onboarding_toii_social {
    return Intl.message(
      'GAO Social',
      name: 'onboarding_toii_social',
      desc: '',
      args: [],
    );
  }

  /// `No Ads\nAI-Personalized.`
  String get onboarding_description1 {
    return Intl.message(
      'No Ads\nAI-Personalized.',
      name: 'onboarding_description1',
      desc: '',
      args: [],
    );
  }

  /// `Decentralized`
  String get onboarding_description2 {
    return Intl.message(
      'Decentralized',
      name: 'onboarding_description2',
      desc: '',
      args: [],
    );
  }

  /// `Connect and\nExplore your way`
  String get onboarding_description3 {
    return Intl.message(
      'Connect and\nExplore your way',
      name: 'onboarding_description3',
      desc: '',
      args: [],
    );
  }

  /// `It's great to see you again!`
  String get login_description {
    return Intl.message(
      'It\'s great to see you again!',
      name: 'login_description',
      desc: '',
      args: [],
    );
  }

  /// `Email or Phone Number`
  String get login_email_or_phone {
    return Intl.message(
      'Email or Phone Number',
      name: 'login_email_or_phone',
      desc: '',
      args: [],
    );
  }

  /// `Password`
  String get login_password {
    return Intl.message('Password', name: 'login_password', desc: '', args: []);
  }

  /// `Forgot Password?`
  String get login_forgot_password {
    return Intl.message(
      'Forgot Password?',
      name: 'login_forgot_password',
      desc: '',
      args: [],
    );
  }

  /// `Sign In`
  String get login_sign_in {
    return Intl.message('Sign In', name: 'login_sign_in', desc: '', args: []);
  }

  /// `Or Join with`
  String get login_or_sign_in_with {
    return Intl.message(
      'Or Join with',
      name: 'login_or_sign_in_with',
      desc: '',
      args: [],
    );
  }

  /// `Login With Wallet`
  String get login_with_wallet {
    return Intl.message(
      'Login With Wallet',
      name: 'login_with_wallet',
      desc: '',
      args: [],
    );
  }

  /// `Don't have an account?`
  String get login_dont_have_account {
    return Intl.message(
      'Don\'t have an account?',
      name: 'login_dont_have_account',
      desc: '',
      args: [],
    );
  }

  /// `Register`
  String get login_register {
    return Intl.message('Register', name: 'login_register', desc: '', args: []);
  }

  /// `Enter\nphone or Email`
  String get register_enter_title {
    return Intl.message(
      'Enter\nphone or Email',
      name: 'register_enter_title',
      desc: '',
      args: [],
    );
  }

  /// `phone or Email`
  String get register_enter_subtitle {
    return Intl.message(
      'phone or Email',
      name: 'register_enter_subtitle',
      desc: '',
      args: [],
    );
  }

  /// `Phone`
  String get register_option_phone {
    return Intl.message(
      'Phone',
      name: 'register_option_phone',
      desc: '',
      args: [],
    );
  }

  /// `Email`
  String get register_option_email {
    return Intl.message(
      'Email',
      name: 'register_option_email',
      desc: '',
      args: [],
    );
  }

  /// `Phone Number`
  String get register_label_phone_number {
    return Intl.message(
      'Phone Number',
      name: 'register_label_phone_number',
      desc: '',
      args: [],
    );
  }

  /// `Already have an account?`
  String get register_already_have_account {
    return Intl.message(
      'Already have an account?',
      name: 'register_already_have_account',
      desc: '',
      args: [],
    );
  }

  /// `Sign In`
  String get register_sign_in {
    return Intl.message(
      'Sign In',
      name: 'register_sign_in',
      desc: '',
      args: [],
    );
  }

  /// `I have read and agree to the Terms of Service and Privacy Policy`
  String get register_terms_agreement {
    return Intl.message(
      'I have read and agree to the Terms of Service and Privacy Policy',
      name: 'register_terms_agreement',
      desc: '',
      args: [],
    );
  }

  /// `Terms of Service`
  String get register_terms_of_service {
    return Intl.message(
      'Terms of Service',
      name: 'register_terms_of_service',
      desc: '',
      args: [],
    );
  }

  /// `Privacy Policy`
  String get register_privacy_policy {
    return Intl.message(
      'Privacy Policy',
      name: 'register_privacy_policy',
      desc: '',
      args: [],
    );
  }

  /// `Continue`
  String get register_continue {
    return Intl.message(
      'Continue',
      name: 'register_continue',
      desc: '',
      args: [],
    );
  }

  /// `Verify Your Phone Number`
  String get register_verify_phone_title {
    return Intl.message(
      'Verify Your Phone Number',
      name: 'register_verify_phone_title',
      desc: '',
      args: [],
    );
  }

  /// `Verify Your Email`
  String get register_verify_email_title {
    return Intl.message(
      'Verify Your Email',
      name: 'register_verify_email_title',
      desc: '',
      args: [],
    );
  }

  /// `Please enter the 6 digit code sent to`
  String get register_verify_phone_subtitle {
    return Intl.message(
      'Please enter the 6 digit code sent to',
      name: 'register_verify_phone_subtitle',
      desc: '',
      args: [],
    );
  }

  /// `00:24`
  String get register_timer_placeholder {
    return Intl.message(
      '00:24',
      name: 'register_timer_placeholder',
      desc: '',
      args: [],
    );
  }

  /// `Didn't receive the code?`
  String get register_didnt_receive_code {
    return Intl.message(
      'Didn\'t receive the code?',
      name: 'register_didnt_receive_code',
      desc: '',
      args: [],
    );
  }

  /// `Resend`
  String get register_resend_code {
    return Intl.message(
      'Resend',
      name: 'register_resend_code',
      desc: '',
      args: [],
    );
  }

  /// `Confirm`
  String get register_confirm_button {
    return Intl.message(
      'Confirm',
      name: 'register_confirm_button',
      desc: '',
      args: [],
    );
  }

  /// `Create your username`
  String get register_create_username_title {
    return Intl.message(
      'Create your username',
      name: 'register_create_username_title',
      desc: '',
      args: [],
    );
  }

  /// `Pick a name to use on Toii`
  String get register_create_username_subtitle {
    return Intl.message(
      'Pick a name to use on Toii',
      name: 'register_create_username_subtitle',
      desc: '',
      args: [],
    );
  }

  /// `Username`
  String get register_username_field_label {
    return Intl.message(
      'Username',
      name: 'register_username_field_label',
      desc: '',
      args: [],
    );
  }

  /// `Username not empty`
  String get register_create_username_error {
    return Intl.message(
      'Username not empty',
      name: 'register_create_username_error',
      desc: '',
      args: [],
    );
  }

  /// `Create password`
  String get register_create_password {
    return Intl.message(
      'Create password',
      name: 'register_create_password',
      desc: '',
      args: [],
    );
  }

  /// `Password`
  String get register_password_label {
    return Intl.message(
      'Password',
      name: 'register_password_label',
      desc: '',
      args: [],
    );
  }

  /// `Confirm Password`
  String get register_confirm_password_label {
    return Intl.message(
      'Confirm Password',
      name: 'register_confirm_password_label',
      desc: '',
      args: [],
    );
  }

  /// `Passwords do not match`
  String get register_password_mismatch_error {
    return Intl.message(
      'Passwords do not match',
      name: 'register_password_mismatch_error',
      desc: '',
      args: [],
    );
  }

  /// `Your password should contain:`
  String get register_password_requirements_title {
    return Intl.message(
      'Your password should contain:',
      name: 'register_password_requirements_title',
      desc: '',
      args: [],
    );
  }

  /// `8 or more characters.`
  String get register_password_rule_1 {
    return Intl.message(
      '8 or more characters.',
      name: 'register_password_rule_1',
      desc: '',
      args: [],
    );
  }

  /// `At least one upper case character.`
  String get register_password_rule_2 {
    return Intl.message(
      'At least one upper case character.',
      name: 'register_password_rule_2',
      desc: '',
      args: [],
    );
  }

  /// `At least one symbol.`
  String get register_password_rule_3 {
    return Intl.message(
      'At least one symbol.',
      name: 'register_password_rule_3',
      desc: '',
      args: [],
    );
  }

  /// `Create a wallet`
  String get register_create_wallet_title {
    return Intl.message(
      'Create a wallet',
      name: 'register_create_wallet_title',
      desc: '',
      args: [],
    );
  }

  /// `Unlock with Face ID?`
  String get register_unlock_with_face_id {
    return Intl.message(
      'Unlock with Face ID?',
      name: 'register_unlock_with_face_id',
      desc: '',
      args: [],
    );
  }

  /// `Enter a PIN Code`
  String get register_enter_pin_title {
    return Intl.message(
      'Enter a PIN Code',
      name: 'register_enter_pin_title',
      desc: '',
      args: [],
    );
  }

  /// `Creating your wallet...`
  String get register_creating_wallet {
    return Intl.message(
      'Creating your wallet...',
      name: 'register_creating_wallet',
      desc: '',
      args: [],
    );
  }

  /// `Protect your wallet. PIN code increases wallet security in the event your phone is stolen`
  String get register_enter_pin_description {
    return Intl.message(
      'Protect your wallet. PIN code increases wallet security in the event your phone is stolen',
      name: 'register_enter_pin_description',
      desc: '',
      args: [],
    );
  }

  /// `Confirm your PIN code`
  String get register_confirm_pin_title {
    return Intl.message(
      'Confirm your PIN code',
      name: 'register_confirm_pin_title',
      desc: '',
      args: [],
    );
  }

  /// `Pin not match`
  String get register_create_pin_error {
    return Intl.message(
      'Pin not match',
      name: 'register_create_pin_error',
      desc: '',
      args: [],
    );
  }

  /// `Biometric not support`
  String get biometric_not_support {
    return Intl.message(
      'Biometric not support',
      name: 'biometric_not_support',
      desc: '',
      args: [],
    );
  }

  /// `Use Face ID`
  String get biometric_use_face_id {
    return Intl.message(
      'Use Face ID',
      name: 'biometric_use_face_id',
      desc: '',
      args: [],
    );
  }

  /// `Use Touch ID`
  String get biometric_use_touch_id {
    return Intl.message(
      'Use Touch ID',
      name: 'biometric_use_touch_id',
      desc: '',
      args: [],
    );
  }

  /// `Use Fingerprint`
  String get biometric_use_fingerprint {
    return Intl.message(
      'Use Fingerprint',
      name: 'biometric_use_fingerprint',
      desc: '',
      args: [],
    );
  }
}

class AppLocalizationDelegate extends LocalizationsDelegate<S> {
  const AppLocalizationDelegate();

  List<Locale> get supportedLocales {
    return const <Locale>[Locale.fromSubtags(languageCode: 'en')];
  }

  @override
  bool isSupported(Locale locale) => _isSupported(locale);
  @override
  Future<S> load(Locale locale) => S.load(locale);
  @override
  bool shouldReload(AppLocalizationDelegate old) => false;

  bool _isSupported(Locale locale) {
    for (var supportedLocale in supportedLocales) {
      if (supportedLocale.languageCode == locale.languageCode) {
        return true;
      }
    }
    return false;
  }
}
