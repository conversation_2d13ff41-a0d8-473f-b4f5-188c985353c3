import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:readmore/readmore.dart';
import 'package:toii_social/cubit/user/follow_management/follow_management_cubit.dart';
import 'package:toii_social/model/post/post_model.dart';
import 'package:toii_social/router/app_router.dart';
import 'package:toii_social/screen/home/<USER>/home_details_screen.dart';
import 'package:toii_social/screen/home/<USER>/post/post_action_bar.dart';
import 'package:toii_social/screen/home/<USER>/post/post_header.dart';
import 'package:toii_social/screen/home/<USER>/post/post_image_gallery.dart';
import 'package:toii_social/utils/time_utils.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';
import 'package:toii_social/widget/images/avatar_widget.dart';

class HomeItemPostWidget extends StatefulWidget {
  final PostModel post;
  final bool isNotOnTap;
  final bool isShowActionMore;
  const HomeItemPostWidget({
    super.key,
    required this.post,
    required this.isNotOnTap,
    required this.isShowActionMore,
  });

  @override
  State<HomeItemPostWidget> createState() => _HomeItemPostWidgetState();
}

class _HomeItemPostWidgetState extends State<HomeItemPostWidget> {
  bool _isMainContentExpanded = false;
  bool _isRepostContentExpanded = false;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    // Check if this is a repost
    if (widget.post.isRepost == true) {
      return Padding(
        padding: const EdgeInsets.symmetric(vertical: 8),
        child: _buildRepostLayout(context, theme),
      );
    } else {
      return Padding(
        padding: const EdgeInsets.symmetric(vertical: 8),
        child: _buildNormalLayout(context, theme),
      );
    }
  }

  Widget _buildNormalLayout(BuildContext context, ThemeData theme) {
    return Stack(
      alignment: Alignment.bottomCenter,
      children: [
        GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: () {
            if (!widget.isNotOnTap) {
              context.push(
                RouterEnums.homeDetails.routeName,
                extra: HomeDetailsArguments(postModel: widget.post),
              );
            }
          },
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  decoration: BoxDecoration(
                    color: theme.black50,
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(16),
                      topRight: Radius.circular(16),
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      PostHeader(
                        post: widget.post,
                        isShowActionMore: widget.isShowActionMore,
                      ),
                      const SizedBox(height: 8),
                      if (widget.post.content.isNotEmpty)
                        _buildContentText(
                          context,
                          theme,
                          widget.post.content,
                          trimLines: 2,
                          textStyle: bodyLarge.copyWith(
                            color: theme.neutral800,
                          ),
                          moreStyle: labelLarge.copyWith(
                            color: theme.primaryGreen500,
                          ),
                        ),
                      const SizedBox(height: 16),
                    ],
                  ),
                ),

                PostImageGallery(post: widget.post),
                //    const SizedBox(height: 16),
              ],
            ),
          ),
        ),
        PostActionBar(post: widget.post),
      ],
    );
  }

  Widget _buildRepostLayout(BuildContext context, ThemeData theme) {
    return Stack(
      alignment: Alignment.bottomCenter,
      children: [
        GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: () {
            if (!widget.isNotOnTap) {
              context.push(
                RouterEnums.homeDetails.routeName,
                extra: HomeDetailsArguments(postModel: widget.post),
              );
            }
          },
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
            child: Column(
              children: [
                Stack(
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        color: theme.black50,
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(16),
                          topRight: Radius.circular(16),
                        ),
                      ),
                      child: Column(
                        children: [
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Main repost container
                              Container(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    // Repost header with reposter information
                                    PostHeader(
                                      post: widget.post,
                                      isShowActionMore: widget.isShowActionMore,
                                    ),

                                    // Reposter's content (if any - for quote reposts)
                                    // For now, we'll show a simple repost indicator
                                    const SizedBox(height: 8),
                                  ],
                                ),
                              ),

                              // Gray separator line
                              Container(height: 1, color: theme.neutral200),

                              // Original post content in a container
                              _buildOriginalPostContainer(
                                context,
                                theme,
                                isRepostContent: true,
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 16),
              ],
            ),
          ),
        ),
        PostActionBar(post: widget.post, isHideRepost: true),
      ],
    );
  }

  Widget _buildOriginalPostContainer(
    BuildContext context,
    ThemeData theme, {
    bool isRepostContent = false,
  }) {
    return Container(
      decoration:
          isRepostContent
              ? BoxDecoration()
              : BoxDecoration(
                color: theme.black50,
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(16),
                  bottomRight: Radius.circular(16),
                ),
              ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Original post author info with smaller avatar
          if (widget.post.originalPost?.user != null)
            _buildOriginalPostHeader(theme),

          // Original post content
          if (widget.post.originalPost?.content.isNotEmpty ?? false) ...[
            const SizedBox(height: 8),
            _buildContentText(
              context,
              theme,
              widget.post.originalPost?.content ?? '',
              trimLines: 3,
              textStyle: bodyMedium.copyWith(color: theme.neutral800),
              moreStyle: labelMedium.copyWith(color: theme.primaryGreen500),
              padding: const EdgeInsets.symmetric(horizontal: 12),
              isRepostContent: true,
            ),
          ],

          // Original post media
          if (widget.post.originalPost?.mediaUrls.isNotEmpty ?? false) ...[
            const SizedBox(height: 12),
            Padding(
              padding: EdgeInsets.symmetric(
                horizontal: isRepostContent ? 0 : 12,
              ),
              child: PostImageGallery(
                post: widget.post.originalPost!,
                isRepost: true,
              ),
            ),
          ] else
            SizedBox(height: 32),
        ],
      ),
    );
  }

  Widget _buildOriginalPostHeader(ThemeData theme) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        if (!widget.isNotOnTap) {
          context.push(
            RouterEnums.homeDetails.routeName,
            extra: HomeDetailsArguments(postModel: widget.post),
          );
        }
      },
      child: Padding(
        padding: const EdgeInsets.fromLTRB(12, 12, 12, 0),
        child: GestureDetector(
          onTap: () {
            context.push(
              RouterEnums.userProfile.routeName,
              extra: widget.post.originalPost?.user,
            );
          },
          child: Row(
            children: [
              AvatarWidget(
                size: 32,
                imageUrl: widget.post.originalPost?.user?.avatarUrl,
                name: widget.post.originalPost?.user?.fullName ?? '',
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Flexible(
                          child: Text(
                            (widget.post.originalPost?.user?.fullName ?? '')
                                    .isNotEmpty
                                ? widget.post.originalPost?.user?.fullName ?? ''
                                : widget.post.originalPost?.user?.username ??
                                    widget.post.originalPost?.user?.fullName ??
                                    '',
                            style: titleMedium.copyWith(
                              color: theme.neutral800,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        // Show follow button only if not self and not already following
                        // if (_shouldShowOriginalPostFollowButton()) ...[
                        //   const SizedBox(width: 6),
                        //   Container(
                        //     width: 3,
                        //     height: 3,
                        //     decoration: BoxDecoration(
                        //       color: theme.neutral300,
                        //       borderRadius: BorderRadius.circular(30),
                        //     ),
                        //   ),
                        //   const SizedBox(width: 6),
                        //   _buildOriginalPostFollowButton(theme),
                        // ],
                      ],
                    ),
                    const SizedBox(height: 4),
                    Text(
                      timeAgoSinceDate(
                        widget.post.originalPost?.updatedAt ?? '',
                      ),
                      style: labelSmall.copyWith(color: theme.neutral300),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildContentText(
    BuildContext context,
    ThemeData theme,
    String content, {
    required int trimLines,
    required TextStyle textStyle,
    required TextStyle moreStyle,
    EdgeInsets padding = const EdgeInsets.symmetric(horizontal: 12),
    bool isRepostContent = false,
  }) {
    return Padding(
      padding: padding,
      child: LayoutBuilder(
        builder: (context, constraints) {
          // Create a text painter to measure if text needs truncation
          final textPainter = TextPainter(
            text: TextSpan(text: content, style: textStyle),
            maxLines: trimLines,
            textDirection: TextDirection.ltr,
          );
          textPainter.layout(maxWidth: constraints.maxWidth);

          final bool needsTruncation = textPainter.didExceedMaxLines;
          final bool isExpanded =
              isRepostContent
                  ? _isRepostContentExpanded
                  : _isMainContentExpanded;

          return GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () {
              if (needsTruncation && !isExpanded) {
                // Text is truncated and not expanded - expand it
                setState(() {
                  if (isRepostContent) {
                    _isRepostContentExpanded = true;
                  } else {
                    _isMainContentExpanded = true;
                  }
                });
              } else {
                // Text is either not truncated or is expanded - navigate to detail screen
                if (!widget.isNotOnTap) {
                  context.push(
                    RouterEnums.homeDetails.routeName,
                    extra: HomeDetailsArguments(postModel: widget.post),
                  );
                }
              }
            },
            child:
                isExpanded
                    ? Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(content, style: textStyle),
                        const SizedBox(height: 4),
                        GestureDetector(
                          onTap: () {
                            // Handle "Show less" tap - collapse the text
                            setState(() {
                              if (isRepostContent) {
                                _isRepostContentExpanded = false;
                              } else {
                                _isMainContentExpanded = false;
                              }
                            });
                          },
                          child: Text(' Show less', style: moreStyle),
                        ),
                      ],
                    )
                    : ReadMoreText(
                      content,
                      trimLines: trimLines,
                      trimMode: TrimMode.Line,
                      trimCollapsedText: needsTruncation ? ' See more' : '',
                      trimExpandedText: '',
                      style: textStyle,
                      moreStyle: moreStyle,
                    ),
          );
        },
      ),
    );
  }

  bool _shouldShowOriginalPostFollowButton() {
    // Don't show follow button if:
    // 1. No original post or user
    // 2. Original post is by current user (isSelf is true)
    // 3. Already following the original post user
    if (widget.post.originalPost?.user?.id == null) return false;
    if (widget.post.originalPost?.relationship?.isSelf == true) return false;
    if (widget.post.originalPost?.relationship?.isFollowing == true)
      return false;

    return true;
  }

  Widget _buildOriginalPostFollowButton(ThemeData theme) {
    final originalPost = widget.post.originalPost;
    if (originalPost?.user?.id == null) return const SizedBox.shrink();

    return BlocProvider(
      create:
          (context) => FollowManagementCubit(
            userId: originalPost!.user!.id,
            initialFollowingStatus:
                originalPost.relationship?.isFollowing ?? false,
          ),
      child: BlocBuilder<FollowManagementCubit, FollowManagementState>(
        builder: (context, state) {
          return GestureDetector(
            onTap: () {
              context.read<FollowManagementCubit>().toggleFollow();
            },
            child: Text(
              state.isFollowing ? 'Following' : 'Follow',
              style: titleMedium.copyWith(
                color:
                    state.isFollowing
                        ? theme.neutral400
                        : theme.primaryGreen500,
              ),
            ),
          );
        },
      ),
    );
  }
}
