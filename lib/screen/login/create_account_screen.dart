import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:lottie/lottie.dart';
import 'package:toii_social/cubit/auth/create_account/create_account_cubit.dart';
import 'package:toii_social/gen/assets.gen.dart';
import 'package:toii_social/router/app_router.dart';

class CreateAccountScreen extends StatefulWidget {
  final String? address;
  const CreateAccountScreen({super.key, this.address});

  @override
  State<CreateAccountScreen> createState() => _CreateAccountScreenState();
}

class _CreateAccountScreenState extends State<CreateAccountScreen> {
  @override
  void initState() {
    super.initState();
    // initPlatformState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => CreateAccountCubit()..createAccount(widget.address),
      child: BlocConsumer<CreateAccountCubit, CreateAccountState>(
        listener: (context, state) async {
          if (state.status.isFailure) {
            Navigator.of(context).pop(state.errorMessage);
          }
          if (state.status.isSuccess) {
            context.go(RouterEnums.inital.routeName);
          }
        },

        builder: (context, state) {
          return Stack(
            children: [
              Assets.images.bgLoadingLogin.image(),
              Lottie.asset(
                Assets.json.processing,

                ///     'assets/LottieLogo1.json',
                //  controller: _controller,
                // onLoaded: (composition) {
                //   // Configure the AnimationController with the duration of the
                //   // Lottie file and start the animation.
                //   _controller
                //     ..duration = composition.duration
                //     ..forward();
                // },
              ),
            ],
          );
        },
      ),
    );
  }
}
