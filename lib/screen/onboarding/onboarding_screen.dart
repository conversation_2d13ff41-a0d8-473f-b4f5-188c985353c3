import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:toii_social/cubit/theme/theme_cubit.dart';
import 'package:toii_social/router/app_router.dart';
import 'package:toii_social/screen/onboarding/model/onboarding_model.dart';
import 'package:toii_social/screen/onboarding/widget/dot_page_indicator.dart';
import 'package:toii_social/widget/button/button.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';

class OnboardingScreen extends StatefulWidget {
  const OnboardingScreen({super.key});

  @override
  State<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen>
    with TickerProviderStateMixin {
  final _pageController = PageController();

  late List<Widget> _pages;

  late TabController _selectorController;

  int _currentPage = 0;

  Duration get _duration => const Duration(milliseconds: 800);

  @override
  void initState() {
    _pages = listOnboarding.map((e) => _itemOnboarding(e)).toList();

    _selectorController = TabController(length: _pages.length, vsync: this);

    super.initState();
  }

  @override
  void dispose() {
    _pageController.dispose();
    _selectorController.dispose();
    super.dispose();
  }

  void _onPageChanged(int index) {
    _currentPage = index;
    _selectorController.animateTo(index, duration: _duration);
  }

  void _changePage(int index) {
    if (!_pageController.hasClients) return;

    index == 0
        ? _pageController.jumpToPage(index)
        : _pageController.animateToPage(
          index,
          duration: _duration,
          curve: Curves.ease,
        );
  }

  Widget _itemOnboarding(OnboardingModel item) {
    return Stack(
      children: [
        item.image.image(fit: BoxFit.fitWidth),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 91),
            Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                const SizedBox(width: 16),
                Text(
                  item.message,
                  style: headlineSmall.copyColor(themeData.neutral500),
                ),
                Text(
                  item.message1,
                  style: headlineLarge.copyColor(themeData.primaryGreen500),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Padding(
              padding: const EdgeInsets.only(left: 16),
              child: Text(
                item.description,
                style: displayMedium.copyWith(
                  fontSize: 42,
                  height: 52 / 42,
                  color: themeData.black800,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(children: [_carouselSliderWidget(), _nextButton()]),
    );
  }

  Widget _carouselSliderWidget() {
    return PageView.builder(
      physics: const AlwaysScrollableScrollPhysics(),
      controller: _pageController,
      itemCount: _pages.length,
      onPageChanged: _onPageChanged,
      itemBuilder: (BuildContext context, int index) {
        return _pages[index % _pages.length];
      },
    );
  }

  Widget _nextButton() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        Expanded(child: Container()),
        DotPageIndicator(tabController: _selectorController),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          width: double.infinity,
          height: 82 + MediaQuery.of(context).padding.bottom,
          //     color: themeData.primaryGreen500,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Text(
              //   S.current.onboarding_explore_the_future,
              //   style: titleMedium.copyColor(themeData.neutral50),
              // ),
              // GestureDetector(
              //   onTap: () {
              //     if (_currentPage < _pages.length - 1) {
              //       _changePage(_currentPage + 1);
              //     } else {
              //       context.go(RouterEnums.login.routeName);
              //     }
              //   },
              //   child: SvgPicture.asset(Assets.icons.icArrowOnboarding.path),
              // ),
              Expanded(
                child: TSButton.primary(
                  size: ButtonSize.block,
                  onPressed: () {
                    context.push(RouterEnums.createAcount.routeName);
                  },
                  title: 'JOIN GAO SOCIAL',
                ),
              ),
              const SizedBox(width: 2),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 22),
                alignment: Alignment.center,
                height: 48,
                decoration: BoxDecoration(
                  color: themeData.black800,
                  borderRadius: BorderRadius.circular(100),
                ),
                child: Text(
                  'Restore',
                  style: titleMedium.copyColor(themeData.white900),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
