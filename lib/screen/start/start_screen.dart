import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:toii_social/cubit/theme/theme_cubit.dart';
import 'package:toii_social/gen/assets.gen.dart';
import 'package:toii_social/model/user/user_model.dart';
import 'package:toii_social/router/app_router.dart';
import 'package:toii_social/utils/keychain/keychain_service.dart';
import 'package:toii_social/widget/button/button.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';
import 'package:toii_social/widget/images/avatar_widget.dart';

class StartScreen extends StatefulWidget {
  const StartScreen({super.key});

  @override
  State<StartScreen> createState() => _StartScreenState();
}

class _StartScreenState extends State<StartScreen> {
  List<String> walletList = [];
  Map<String, UserModel> profileData = {};
  @override
  void initState() {
    super.initState();
    getData();
  }

  void getData() async {
    final list = await KeychainService.instance.getListWallet();
    final profile = await KeychainService.instance.getProfile();
    final map = profile.map(
      (key, value) => MapEntry(key, UserModel.fromJson(value)),
    );
    setState(() {
      walletList = list;
      profileData = map;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      extendBodyBehindAppBar: true,
      body: Container(
        height: double.infinity,
        width: double.infinity,
        decoration: BoxDecoration(
          image: DecorationImage(
            image: Assets.images.bgHome.provider(),
            fit: BoxFit.cover,
          ),
        ),
        child: SafeArea(
          bottom: false,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Assets.icons.newLogo.image(),
                Expanded(
                  child: GridView.count(
                    childAspectRatio: 1 / 1.5,
                    mainAxisSpacing: 16,
                    crossAxisSpacing: 16,
                    crossAxisCount: 2,
                    children: List.generate(walletList.length, (index) {
                      return Center(
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            AvatarWidget(
                              size: 154,
                              imageUrl:
                                  profileData[walletList[index]]?.avatarUrl,
                              name:
                                  profileData[walletList[index]]?.username ??
                                  '',
                            ),
                            const SizedBox(height: 16),
                            Text(
                              profileData[walletList[index]]?.username ?? "",
                              style: titleMedium.copyColor(
                                themeData.neutral800,
                              ),
                            ),
                            const SizedBox(height: 8),
                            TSButton.primary(
                              title: "Continue",
                              onPressed: () {
                                context.push(
                                  RouterEnums.createAcount.routeName,
                                  extra: walletList[index],
                                );
                              },
                            ),
                          ],
                        ),
                      );
                    }),
                  ),
                ),
                _nextButton(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _nextButton() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        const SizedBox(height: 16),
        Container(
          width: double.infinity,
          height: 82 + MediaQuery.of(context).padding.bottom,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: GestureDetector(
                  onTap: () {
                    context.push(RouterEnums.createAcount.routeName);
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 22),
                    alignment: Alignment.center,
                    height: 48,
                    decoration: BoxDecoration(
                      border: Border.all(color: themeData.primaryGreen500),
                      // color: themeData.black800,
                      borderRadius: BorderRadius.circular(100),
                    ),
                    child: Text(
                      'Activate Soul',
                      style: titleMedium.copyColor(themeData.primaryGreen500),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 22),
                alignment: Alignment.center,
                height: 48,
                decoration: BoxDecoration(
                  border: Border.all(color: themeData.primaryGreen500),
                  // color: themeData.black800,
                  borderRadius: BorderRadius.circular(100),
                ),
                child: Text(
                  'Restore',
                  style: titleMedium.copyColor(themeData.primaryGreen500),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
