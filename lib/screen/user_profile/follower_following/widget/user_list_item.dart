import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:toii_social/cubit/auth/profile/profile_cubit.dart';
import 'package:toii_social/cubit/user/follow_management/follow_management_cubit.dart';
import 'package:toii_social/model/user/user_model.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';

class UserListItem extends StatelessWidget {
  const UserListItem({
    super.key,
    required this.user,
    this.isFollowing = false,
    this.onFollowStatusChanged,
    this.isMyAccount = true,
  });

  final UserModel user;
  final bool isFollowing;
  final VoidCallback? onFollowStatusChanged;
  final bool isMyAccount;

  /// Check if the current user is the same as the follower (self-follow case)
  bool get _isSelfFollow {
    final currentUserId = GetIt.instance<ProfileCubit>().state.userModel?.id;
    return currentUserId != null && currentUserId == user.id;
  }

  @override
  Widget build(BuildContext context) {
    final themeData = context.themeData;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        children: [
          // Avatar
          CircleAvatar(
            radius: 20,
            backgroundImage:
                user.avatarUrl != null && user.avatarUrl!.isNotEmpty
                    ? NetworkImage(user.avatarUrl!)
                    : null,
            child:
                user.avatarUrl == null || user.avatarUrl!.isEmpty
                    ? Text(
                      (user.fullName ?? user.username).isNotEmpty
                          ? (user.fullName ?? user.username)[0].toUpperCase()
                          : '',
                      style: titleMedium.copyWith(color: themeData.neutral50),
                    )
                    : null,
          ),
          const SizedBox(width: 12),
          // User Info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  (user.fullName ?? '').isNotEmpty
                      ? user.fullName ?? ''
                      : user.username,
                  style: titleMedium.copyWith(color: themeData.neutral800),
                ),
                if (user.username.isNotEmpty)
                  Text(
                    user.email ?? user.phoneNumber ?? '',
                    style: bodyMedium.copyWith(color: themeData.neutral400),
                  ),
              ],
            ),
          ),
          const SizedBox(width: 12),
          // Follow Button - Hide for self-follow case
          if (!_isSelfFollow)
            BlocProvider(
              create:
                  (context) => FollowManagementCubit(
                    userId: user.id,
                    initialFollowingStatus: isFollowing,
                  ),
              child: BlocListener<FollowManagementCubit, FollowManagementState>(
                listener: (context, state) {
                  // Call callback when follow status changes successfully
                  if (state.status.isSuccess && onFollowStatusChanged != null) {
                    onFollowStatusChanged!();
                  }
                },
                child: BlocBuilder<
                  FollowManagementCubit,
                  FollowManagementState
                >(
                  builder: (context, state) {
                    return ElevatedButton(
                      onPressed:
                          state.status.isLoading
                              ? null
                              : () {
                                context
                                    .read<FollowManagementCubit>()
                                    .toggleFollow();
                              },
                      style: ElevatedButton.styleFrom(
                        backgroundColor:
                            state.isFollowing
                                ? themeData.neutral800.withValues(
                                  alpha: 0.8,
                                ) // Dark background for "Following"
                                : themeData
                                    .primaryGreen500, // Green background for "Follow back"
                        foregroundColor:
                            themeData.neutral50, // White text for both states
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                        ),
                        minimumSize: const Size(0, 32),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(
                            48,
                          ), // Rounded button as per Figma
                        ),
                      ),
                      child:
                          state.status.isLoading
                              ? SizedBox(
                                width: 16,
                                height: 16,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    themeData
                                        .neutral50, // Always white for consistency
                                  ),
                                ),
                              )
                              : Text(
                                state.isFollowing
                                    ? 'Following'
                                    : (isMyAccount ? 'Follow back' : 'Follow'),
                                style: bodyMedium.copyWith(
                                  color:
                                      themeData.neutral50, // Always white text
                                ),
                              ),
                    );
                  },
                ),
              ),
            ),
        ],
      ),
    );
  }
}
