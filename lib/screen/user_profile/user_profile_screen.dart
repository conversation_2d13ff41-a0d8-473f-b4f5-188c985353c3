import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:toii_social/cubit/auth/profile/profile_cubit.dart';
import 'package:toii_social/cubit/user/user_stats/user_stats_cubit.dart';
import 'package:toii_social/gen/assets.gen.dart';
import 'package:toii_social/model/user/user_model.dart';
import 'package:toii_social/router/app_router.dart';
import 'package:toii_social/screen/user_profile/widget/profile_card.dart';
import 'package:toii_social/screen/user_profile/widget/user_profile_more_actions_bottom_sheet.dart';
import 'package:toii_social/screen/user_profile/widget/user_profile_post_list.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';
import 'package:toii_social/widget/images/avatar_widget.dart';
import 'package:toii_social/widget/images/cached_network_image_widget.dart';

class UserProfileScreen extends StatefulWidget {
  const UserProfileScreen({super.key, this.user});
  final UserModel? user;

  @override
  State<UserProfileScreen> createState() => _UserProfileScreenState();
}

class _UserProfileScreenState extends State<UserProfileScreen> {
  bool isMyProfile = false;
  final PageController _pageController = PageController();
  int _currentPage = 0;

  @override
  void initState() {
    isMyProfile =
        widget.user?.id == GetIt.instance<ProfileCubit>().state.userModel?.id;
    super.initState();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _onSwipeUp() {
    if (_currentPage == 0) {
      _pageController.animateToPage(
        1,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _onSwipeDown() {
    if (_currentPage == 1) {
      _pageController.animateToPage(
        0,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final themeData = context.themeData;
    return BlocProvider(
      create:
          (context) => UserStatsCubit()..getUserStats(widget.user?.id ?? ""),
      child: Scaffold(
        backgroundColor: themeData.neutral100,
        body: PageView(
          controller: _pageController,
          scrollDirection: Axis.vertical,
          onPageChanged: (index) {
            setState(() {
              _currentPage = index;
            });
          },
          children: [
            // First page: Profile view
            _buildProfilePage(context, themeData),
            // Second page: Tabs + Post List
            _buildPostListPage(context, themeData),
          ],
        ),
      ),
    );
  }

  Widget _buildProfilePage(BuildContext context, ThemeData themeData) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading:
            !isMyProfile
                ? Container(
                  margin: const EdgeInsets.all(8),
                  child: Material(
                    color: themeData.neutral400.withAlpha(200),
                    borderRadius: BorderRadius.circular(16),
                    child: InkWell(
                      borderRadius: BorderRadius.circular(8),
                      onTap: () => Navigator.of(context).pop(),
                      child: SizedBox(
                        width: 35,
                        height: 45,
                        child: Icon(
                          Icons.chevron_left,
                          color: Colors.white,
                          size: 24,
                        ),
                      ),
                    ),
                  ),
                )
                : null,
        title: Text(
          (widget.user?.fullName ?? '').isNotEmpty
              ? widget.user?.fullName ?? ''
              : widget.user?.username ?? '',
          //   widget.user?.fullName ?? widget.user?.username ?? '-',
          style: titleLarge.copyWith(color: themeData.neutral50),
        ),
        centerTitle: true,
        actions: [
          if (!isMyProfile)
            IconButton(
              icon: Icon(Icons.more_horiz, color: themeData.neutral50),
              onPressed: () {
                showUserProfileMoreActionsBottomSheet(
                  context: context,
                  user: widget.user,
                );
              },
            ),
          if (isMyProfile)
            Padding(
              padding: const EdgeInsets.only(right: 16),
              child: GestureDetector(
                onTap: () {
                  context.push(RouterEnums.settingProfile.routeName);
                },
                child: Assets.icons.icSetting.svg(),
              ),
            ),
        ],
      ),
      body: GestureDetector(
        onPanEnd: (details) {
          // Detect swipe up gesture
          if (details.velocity.pixelsPerSecond.dy < -500) {
            _onSwipeUp();
          }
        },
        child: Stack(
          children: [
            // Background image
            widget.user?.backgroundUrl != null &&
                    widget.user!.backgroundUrl!.isNotEmpty
                ? CachedNetworkImageWidget(
                  imageUrl: widget.user!.backgroundUrl!,
                  fit: BoxFit.cover,
                  width: double.infinity,
                  height: double.infinity,
                )
                : Assets.images.defaultBackground.image(
                  fit: BoxFit.cover,
                  width: double.infinity,
                  height: double.infinity,
                ),
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [Colors.black.withOpacity(0.25), Colors.transparent],
                ),
              ),
            ),
            // Profile content
            SafeArea(
              bottom: false,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Align(
                    alignment: Alignment.bottomCenter,
                    child: Padding(
                      padding: const EdgeInsets.only(bottom: 0),
                      child: ProfileCard(
                        user: widget.user,
                        isMyProfile: isMyProfile,
                      ),
                    ),
                  ),
                  const SizedBox(height: 12),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      vertical: 16,
                      horizontal: 20,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(24),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.03),
                          blurRadius: 8,
                          offset: Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Column(
                      children: [
                        GestureDetector(
                          onTap: _onSwipeUp,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Assets.icons.icScrollDown.svg(),
                              const SizedBox(width: 16),
                              Text(
                                'Swipe up to see posts',
                                style: TextStyle(
                                  color: Colors.grey[700],
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ),
                        SizedBox(
                          height:
                              (isMyProfile
                                  ? MediaQuery.of(context).padding.bottom
                                  : 0) +
                              (Platform.isIOS ? 50 : 40),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPostListPage(BuildContext context, ThemeData themeData) {
    return Scaffold(
      backgroundColor: themeData.neutral100,
      body: Column(
        children: [
          // Custom profile header to replace AppBar
          Container(
            height: 70 + MediaQuery.of(context).padding.top,
            padding: EdgeInsets.only(top: MediaQuery.of(context).padding.top),
            decoration: BoxDecoration(color: themeData.neutral100),
            child: Row(
              children: [
                // Profile header content
                Expanded(
                  child: GestureDetector(
                    onTap: _onSwipeDown,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        AvatarWidget(
                          size: 32,
                          imageUrl: widget.user?.avatarUrl,
                          name: widget.user?.fullName ?? '',
                        ),
                        const SizedBox(width: 16),
                        Text(
                          (widget.user?.fullName ?? '').isNotEmpty
                              ? widget.user?.fullName ?? ''
                              : widget.user?.username ??
                                  widget.user?.fullName ??
                                  '',
                          style: titleMedium.copyWith(
                            color: themeData.neutral800,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Assets.icons.icScrollUp.svg(),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          // Post list content
          Expanded(child: UserProfilePostList(user: widget.user)),
        ],
      ),
    );
  }
}
